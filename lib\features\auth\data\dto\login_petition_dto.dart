
import 'package:mbe_app/core/dto/base_petition_dto.dart';

class LoginPetitionDto extends BasePetitionDto {
  final String email;
  final String password;

  const LoginPetitionDto({
    required this.email,
    required this.password,
  }) : super(
    action: 'login',
    lang: 'es',
  );

  @override
  Map<String, dynamic> toJson() {
    return {
      ...super.toJson(),
      'country': 'VE',
      'login_email': email,
      'login_password': password,
    };
  }
}