import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/package.dart';
import '../repositories/packages_repository.dart';

class GetPackages implements UseCase<PackagesResponse, GetPackagesParams> {
  final PackagesRepository repository;

  GetPackages(this.repository);

  @override
  Future<Either<Failure, PackagesResponse>> call(GetPackagesParams params) async {
    return await repository.getPackages(
      userId: params.userId,
      page: params.page,
      status: params.status,
      type: params.type,
      guia: params.guia,
    );
  }
}

class GetPackagesParams {
  final String userId;
  final int page;
  final String? status;
  final String? type;
  final String? guia;

  GetPackagesParams({
    required this.userId,
    this.page = 1,
    this.status,
    this.type,
    this.guia,
  });
}
