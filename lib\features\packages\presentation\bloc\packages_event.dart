abstract class PackagesEvent {
  const PackagesEvent();
}

class LoadPackages extends PackagesEvent {
  final String userId;
  final int page;
  final String? status;
  final String? type;
  final String? guia;
  final bool isRefresh;

  const LoadPackages({
    required this.userId,
    this.page = 1,
    this.status,
    this.type,
    this.guia,
    this.isRefresh = false,
  });
}

class LoadMorePackages extends PackagesEvent {
  final String userId;
  final int page;
  final String? status;
  final String? type;
  final String? guia;

  const LoadMorePackages({
    required this.userId,
    required this.page,
    this.status,
    this.type,
    this.guia,
  });
}

class FilterPackages extends PackagesEvent {
  final String userId;
  final String? status;
  final String? type;
  final String? guia;

  const FilterPackages({
    required this.userId,
    this.status,
    this.type,
    this.guia,
  });
}
