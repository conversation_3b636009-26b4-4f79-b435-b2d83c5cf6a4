import 'package:flutter/material.dart';

class PuntosPage extends StatelessWidget {
  const PuntosPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.stars,
            size: 80,
            color: Colors.grey,
          ),
          <PERSON>zedBox(height: 16),
          Text(
            'Puntos',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Esta página está en construcción',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }
}
