import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../../features/auth/domain/entities/user.dart';

abstract class UserStorageService {
  Future<void> saveUser(User user);
  Future<User?> getUser();
  Future<void> clearUser();
  Future<String?> getUserId();
}

class UserStorageServiceImpl implements UserStorageService {
  static const String _userKey = 'user_data';

  @override
  Future<void> saveUser(User user) async {
    final prefs = await SharedPreferences.getInstance();
    final userJson = _userToJson(user);
    await prefs.setString(_userKey, json.encode(userJson));
  }

  @override
  Future<User?> getUser() async {
    final prefs = await SharedPreferences.getInstance();
    final userString = prefs.getString(_userKey);
    if (userString != null) {
      final userJson = json.decode(userString);
      return _userFromJson(userJson);
    }
    return null;
  }

  @override
  Future<void> clearUser() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userKey);
  }

  @override
  Future<String?> getUserId() async {
    final user = await getUser();
    return user?.userId;
  }

  Map<String, dynamic> _userToJson(User user) {
    return {
      'userId': user.userId,
      'email': user.email,
      'fullname': user.fullname,
      'storeId': user.storeId,
      'status': user.status,
      'registered': user.registered,
      'iso': user.iso,
      'salesTaxName': user.salesTaxName,
      'paymentEnable': user.paymentEnable,
      'calcEnable': user.calcEnable,
      'trackEnable': user.trackEnable,
      'deliveryEnable': user.deliveryEnable,
      'prealertEnable': user.prealertEnable,
      'pointsEnable': user.pointsEnable,
      'referralEnable': user.referralEnable,
      'medSys': user.medSys,
      'eboxAddress': {
        'line1': user.eboxAddress.line1,
        'line2': user.eboxAddress.line2,
        'line3': user.eboxAddress.line3,
        'line4': user.eboxAddress.line4,
      },
    };
  }

  User _userFromJson(Map<String, dynamic> json) {
    return User(
      userId: json['userId'],
      email: json['email'],
      fullname: json['fullname'],
      storeId: json['storeId'],
      status: json['status'],
      registered: json['registered'],
      iso: json['iso'],
      salesTaxName: json['salesTaxName'],
      paymentEnable: json['paymentEnable'],
      calcEnable: json['calcEnable'],
      trackEnable: json['trackEnable'],
      deliveryEnable: json['deliveryEnable'],
      prealertEnable: json['prealertEnable'],
      pointsEnable: json['pointsEnable'],
      referralEnable: json['referralEnable'],
      medSys: json['medSys'],
      eboxAddress: EboxAddress(
        line1: json['eboxAddress']['line1'],
        line2: json['eboxAddress']['line2'],
        line3: json['eboxAddress']['line3'],
        line4: json['eboxAddress']['line4'],
      ),
    );
  }
}
