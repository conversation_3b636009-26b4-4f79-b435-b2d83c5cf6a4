import '../../domain/entities/package.dart';

abstract class PackagesState {
  const PackagesState();
}

class PackagesInitial extends PackagesState {}

class PackagesLoading extends PackagesState {}

class PackagesLoadingMore extends PackagesState {
  final List<Package> currentPackages;
  
  const PackagesLoadingMore(this.currentPackages);
}

class PackagesLoaded extends PackagesState {
  final List<Package> packages;
  final bool hasMore;
  final int currentPage;
  final String? currentStatus;
  final String? currentType;
  final String? currentGuia;

  const PackagesLoaded({
    required this.packages,
    required this.hasMore,
    required this.currentPage,
    this.currentStatus,
    this.currentType,
    this.currentGuia,
  });

  PackagesLoaded copyWith({
    List<Package>? packages,
    bool? hasMore,
    int? currentPage,
    String? currentStatus,
    String? currentType,
    String? currentGuia,
  }) {
    return PackagesLoaded(
      packages: packages ?? this.packages,
      hasMore: hasMore ?? this.hasMore,
      currentPage: currentPage ?? this.currentPage,
      currentStatus: currentStatus ?? this.currentStatus,
      currentType: currentType ?? this.currentType,
      currentGuia: currentGuia ?? this.currentGuia,
    );
  }
}

class PackagesError extends PackagesState {
  final String message;
  
  const PackagesError(this.message);
}
