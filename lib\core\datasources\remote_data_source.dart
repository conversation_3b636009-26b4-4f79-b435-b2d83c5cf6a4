import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:mbe_app/core/dto/base_petition_dto.dart';

abstract class RemoteDataSource {
  Future<ResponseType> petition<ResponseType>(BasePetitionDto petitionDto);
}

class RemoteDataSourceImpl implements RemoteDataSource {
  final http.Client client;

  RemoteDataSourceImpl({required this.client});

  @override
  Future<ResponseType> petition<ResponseType>(BasePetitionDto petitionDto) async {
    print(petitionDto.toJson());
    final response = await client.post(
      Uri.parse('https://ws-prod.mbe-latam.com/app/appfunctions-v2.php'),
      body: petitionDto.toJson(),
    );
    if (response.statusCode == 200) {
      final decodedResponse = json.decode(response.body);
      if (decodedResponse['error'] != null && decodedResponse['error'] != '') {
        throw Exception(decodedResponse['error']);
      }
      return decodedResponse as ResponseType;
    } else {
      throw Exception('Login failed');
    }
  }
}