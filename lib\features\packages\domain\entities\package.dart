class Package {
  final int storeDataId;
  final String guia;
  final String referencia;
  final String siglasEstatus;
  final String nombreEstatus;
  final String idebox;
  final String descripcion;
  final double peso;
  final double valor;
  final String courier;
  final String remitente;
  final String fechapro;
  final String lastupd;
  final int progressBar;
  final String totalAPagar;
  final bool orderPending;
  final bool payment;

  Package({
    required this.storeDataId,
    required this.guia,
    required this.referencia,
    required this.siglasEstatus,
    required this.nombreEstatus,
    required this.idebox,
    required this.descripcion,
    required this.peso,
    required this.valor,
    required this.courier,
    required this.remitente,
    required this.fechapro,
    required this.lastupd,
    required this.progressBar,
    required this.totalAPagar,
    required this.orderPending,
    required this.payment,
  });

  factory Package.fromJson(Map<String, dynamic> json) {
    return Package(
      storeDataId: int.tryParse(json['store_data_id'].toString()) ?? 0,
      guia: json['guia'] ?? '',
      referencia: json['referencia'] ?? '',
      siglasEstatus: json['siglas_estatus'] ?? '',
      nombreEstatus: json['nombre_estatus'] ?? '',
      idebox: json['idebox'] ?? '',
      descripcion: json['descripcion'] ?? '',
      peso: double.tryParse(json['peso'].toString()) ?? 0.0,
      valor: double.tryParse(json['valor'].toString()) ?? 0.0,
      courier: json['courier'] ?? '',
      remitente: json['remitente'] ?? '',
      fechapro: json['fechapro'] ?? '',
      lastupd: json['lastupd'] ?? '',
      progressBar: int.tryParse(json['progress_bar'].toString()) ?? 0,
      totalAPagar: json['total_a_pagar'] ?? '',
      orderPending: json['order_pending'] == true || json['order_pending'] == 'true',
      payment: json['payment'] == true || json['payment'] == 'true',
    );
  }
}

class PackagesResponse {
  final List<Package> packages;
  final bool hasMore;
  final int currentPage;

  PackagesResponse({
    required this.packages,
    required this.hasMore,
    required this.currentPage,
  });

  factory PackagesResponse.fromJson(dynamic json) {
    if (json is List) {
      return PackagesResponse(
        packages: json.map((item) => Package.fromJson(item)).toList(),
        hasMore: json.length >= 20, // Assuming 20 items per page
        currentPage: 1,
      );
    }
    return PackagesResponse(
      packages: [],
      hasMore: false,
      currentPage: 1,
    );
  }
}
