import 'package:dartz/dartz.dart';
import 'package:mbe_app/features/auth/data/dto/login_petition_dto.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/entities/user.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../../../core/datasources/remote_data_source.dart';

class AuthRepositoryImpl implements AuthRepository {
  final RemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  AuthRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, User>> login(String email, String password) async {
    if (await networkInfo.isConnected) {
      final petitionDto = LoginPetitionDto(email: email, password: password);
      try {
        final response = await remoteDataSource.petition<Map<String, dynamic>>(petitionDto);
        final user = User.fromJson(response);
        return Right(user);
      } catch (e) {
        return Left(AuthFailure(e.toString().split(':')[1]));
      }
    } else {
      return const Left(NetworkFailure());
    }
  }
}