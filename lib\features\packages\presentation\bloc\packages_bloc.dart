import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/usecases/get_packages.dart';
import 'packages_event.dart';
import 'packages_state.dart';

class PackagesBloc extends Bloc<PackagesEvent, PackagesState> {
  final GetPackages getPackages;

  PackagesBloc({required this.getPackages}) : super(PackagesInitial()) {
    on<LoadPackages>(_onLoadPackages);
    on<LoadMorePackages>(_onLoadMorePackages);
    on<FilterPackages>(_onFilterPackages);
  }

  Future<void> _onLoadPackages(
    LoadPackages event,
    Emitter<PackagesState> emit,
  ) async {
    if (event.isRefresh && state is PackagesLoaded) {
      // Keep current packages visible during refresh
      final currentState = state as PackagesLoaded;
      emit(PackagesLoaded(
        packages: currentState.packages,
        hasMore: currentState.hasMore,
        currentPage: currentState.currentPage,
        currentStatus: event.status,
        currentType: event.type,
        currentGuia: event.guia,
      ));
    } else {
      emit(PackagesLoading());
    }

    final result = await getPackages(
      GetPackagesParams(
        userId: event.userId,
        page: event.page,
        status: event.status,
        type: event.type,
        guia: event.guia,
      ),
    );

    result.fold(
      (failure) => emit(PackagesError(failure.toString())),
      (packagesResponse) => emit(PackagesLoaded(
        packages: packagesResponse.packages,
        hasMore: packagesResponse.hasMore,
        currentPage: packagesResponse.currentPage,
        currentStatus: event.status,
        currentType: event.type,
        currentGuia: event.guia,
      )),
    );
  }

  Future<void> _onLoadMorePackages(
    LoadMorePackages event,
    Emitter<PackagesState> emit,
  ) async {
    if (state is PackagesLoaded) {
      final currentState = state as PackagesLoaded;
      emit(PackagesLoadingMore(currentState.packages));

      final result = await getPackages(
        GetPackagesParams(
          userId: event.userId,
          page: event.page,
          status: event.status,
          type: event.type,
          guia: event.guia,
        ),
      );

      result.fold(
        (failure) => emit(PackagesError(failure.toString())),
        (packagesResponse) => emit(PackagesLoaded(
          packages: [...currentState.packages, ...packagesResponse.packages],
          hasMore: packagesResponse.hasMore,
          currentPage: event.page,
          currentStatus: event.status,
          currentType: event.type,
          currentGuia: event.guia,
        )),
      );
    }
  }

  Future<void> _onFilterPackages(
    FilterPackages event,
    Emitter<PackagesState> emit,
  ) async {
    add(LoadPackages(
      userId: event.userId,
      page: 1,
      status: event.status,
      type: event.type,
      guia: event.guia,
    ));
  }
}
