import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:http/http.dart' as http;
import '../../../packages/presentation/bloc/packages_bloc.dart';
import '../../../packages/presentation/bloc/packages_event.dart';
import '../../../packages/presentation/bloc/packages_state.dart';
import '../../../packages/domain/usecases/get_packages.dart';
import '../../../packages/data/repositories/packages_repository_impl.dart';
import '../../../../core/datasources/remote_data_source.dart';
import '../../../../core/network/network_info.dart';
import '../../../../core/services/user_storage_service.dart';
import '../../../packages/domain/entities/package.dart';

// Simple network info implementation
class NetworkInfoImpl implements NetworkInfo {
  @override
  Future<bool> get isConnected => Future.value(true);
}

class PaquetesPage extends StatefulWidget {
  const PaquetesPage({super.key});

  @override
  State<PaquetesPage> createState() => _PaquetesPageState();
}

class _PaquetesPageState extends State<PaquetesPage> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  String? _selectedStatus;
  String? _selectedType;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) {
      final state = context.read<PackagesBloc>().state;
      if (state is PackagesLoaded && state.hasMore) {
        _loadMorePackages();
      }
    }
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  Future<void> _loadPackages({bool isRefresh = false}) async {
    final userStorage = UserStorageServiceImpl();
    final userId = await userStorage.getUserId();

    if (userId != null && userId.isNotEmpty) {
      context.read<PackagesBloc>().add(
        LoadPackages(
          userId: userId,
          page: 1,
          status: _selectedStatus,
          type: _selectedType,
          guia: _searchController.text.isNotEmpty ? _searchController.text : null,
          isRefresh: isRefresh,
        ),
      );
    } else {
      // Handle case where user is not logged in
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Usuario no encontrado. Por favor, inicia sesión nuevamente.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadMorePackages() async {
    if (!mounted) return;

    final userStorage = UserStorageServiceImpl();
    final userId = await userStorage.getUserId();

    if (!mounted) return;

    final state = context.read<PackagesBloc>().state;

    if (userId != null && userId.isNotEmpty && state is PackagesLoaded) {
      context.read<PackagesBloc>().add(
        LoadMorePackages(
          userId: userId,
          page: state.currentPage + 1,
          status: _selectedStatus,
          type: _selectedType,
          guia: _searchController.text.isNotEmpty ? _searchController.text : null,
        ),
      );
    }
  }

  void _applyFilters() {
    _loadPackages();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => PackagesBloc(
        getPackages: GetPackages(
          PackagesRepositoryImpl(
            remoteDataSource: RemoteDataSourceImpl(
              client: http.Client(),
            ),
            networkInfo: NetworkInfoImpl(),
          ),
        ),
      ),
      child: BlocBuilder<PackagesBloc, PackagesState>(
        builder: (context, state) {
          // Load packages on first build
          if (state is PackagesInitial) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _loadPackages();
            });
          }

          return Column(
            children: [
              _buildFiltersSection(),
              Expanded(
                child: _buildPackagesList(state),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Column(
        children: [
          // Search bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Buscar por guía...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: IconButton(
                icon: const Icon(Icons.clear),
                onPressed: () {
                  _searchController.clear();
                  _applyFilters();
                },
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            onSubmitted: (_) => _applyFilters(),
          ),
          const SizedBox(height: 12),
          // Filter dropdowns
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  initialValue: _selectedStatus,
                  decoration: InputDecoration(
                    labelText: 'Estado',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: const [
                    DropdownMenuItem(value: null, child: Text('Todos')),
                    DropdownMenuItem(value: 'pending', child: Text('Pendiente')),
                    DropdownMenuItem(value: 'in_transit', child: Text('En tránsito')),
                    DropdownMenuItem(value: 'delivered', child: Text('Entregado')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedStatus = value;
                    });
                    _applyFilters();
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: DropdownButtonFormField<String>(
                  initialValue: _selectedType,
                  decoration: InputDecoration(
                    labelText: 'Tipo',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: const [
                    DropdownMenuItem(value: null, child: Text('Todos')),
                    DropdownMenuItem(value: 'package', child: Text('Paquete')),
                    DropdownMenuItem(value: 'document', child: Text('Documento')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedType = value;
                    });
                    _applyFilters();
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPackagesList(PackagesState state) {
    if (state is PackagesLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is PackagesError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
            const SizedBox(height: 16),
            Text(
              'Error al cargar paquetes',
              style: TextStyle(fontSize: 18, color: Colors.red[700]),
            ),
            const SizedBox(height: 8),
            Text(
              state.message,
              style: TextStyle(color: Colors.red[500]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _loadPackages(),
              child: const Text('Reintentar'),
            ),
          ],
        ),
      );
    }

    if (state is PackagesLoaded) {
      if (state.packages.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.inventory_2, size: 64, color: Colors.grey[400]),
              const SizedBox(height: 16),
              Text(
                'No se encontraron paquetes',
                style: TextStyle(fontSize: 18, color: Colors.grey[600]),
              ),
              const SizedBox(height: 8),
              Text(
                'Intenta ajustar los filtros de búsqueda',
                style: TextStyle(color: Colors.grey[500]),
              ),
            ],
          ),
        );
      }

      return RefreshIndicator(
        onRefresh: () => _loadPackages(isRefresh: true),
        child: ListView.builder(
          controller: _scrollController,
          padding: const EdgeInsets.all(16),
          itemCount: state.packages.length + (state.hasMore ? 1 : 0),
          itemBuilder: (context, index) {
            if (index >= state.packages.length) {
              return const Center(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: CircularProgressIndicator(),
                ),
              );
            }

            final package = state.packages[index];
            return _buildPackageCard(package);
          },
        ),
      );
    }

    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.inventory_2, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'Paquetes',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.grey),
          ),
          SizedBox(height: 8),
          Text(
            'Cargando...',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildPackageCard(Package package) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with guide and status
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    'Guía: ${package.guia}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(package.siglasEstatus),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    package.nombreEstatus,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Package details
            _buildDetailRow('Referencia', package.referencia),
            _buildDetailRow('Descripción', package.descripcion),
            _buildDetailRow('Peso', '${package.peso} kg'),
            _buildDetailRow('Valor', '\$${package.valor}'),
            _buildDetailRow('Courier', package.courier),
            _buildDetailRow('Remitente', package.remitente),
            _buildDetailRow('Fecha', package.fechapro),

            if (package.totalAPagar.isNotEmpty) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.orange[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.payment, color: Colors.orange[700], size: 20),
                    const SizedBox(width: 8),
                    Text(
                      'Total a pagar: ${package.totalAPagar}',
                      style: TextStyle(
                        color: Colors.orange[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // Progress bar
            if (package.progressBar > 0) ...[
              const SizedBox(height: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Progreso: ${package.progressBar}%',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 4),
                  LinearProgressIndicator(
                    value: package.progressBar / 100,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(
                      _getStatusColor(package.siglasEstatus),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    if (value.isEmpty) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'delivered':
      case 'entregado':
        return Colors.green;
      case 'in_transit':
      case 'en_transito':
        return Colors.blue;
      case 'pending':
      case 'pendiente':
        return Colors.orange;
      case 'cancelled':
      case 'cancelado':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
