class User {
  String userId;
  String email;
  String fullname;
  String storeId;
  String status;
  int registered;
  String iso;
  String salesTaxName;
  String paymentEnable;
  String calcEnable;
  String trackEnable;
  String deliveryEnable;
  String prealertEnable;
  String pointsEnable;
  String referralEnable;
  String medSys;
  EboxAddress eboxAddress;

  User({
    required this.userId,
    required this.email,
    required this.fullname,
    required this.storeId,
    required this.status,
    required this.registered,
    required this.iso,
    required this.salesTaxName,
    required this.paymentEnable,
    required this.calcEnable,
    required this.trackEnable,
    required this.deliveryEnable,
    required this.prealertEnable,
    required this.pointsEnable,
    required this.referralEnable,
    required this.medSys,
    required this.eboxAddress,
  });

}

class EboxAddress {
  String line1;
  String line2;
  String line3;
  String line4;

  EboxAddress({
    required this.line1,
    required this.line2,
    required this.line3,
    required this.line4,
  });

}
