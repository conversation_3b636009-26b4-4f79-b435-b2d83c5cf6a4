class User {
  String userId;
  String email;
  String fullname;
  String storeId;
  String status;
  int registered;
  String iso;
  String salesTaxName;
  String paymentEnable;
  String calcEnable;
  String trackEnable;
  String deliveryEnable;
  String prealertEnable;
  String pointsEnable;
  String referralEnable;
  String medSys;
  EboxAddress eboxAddress;

  User({
    required this.userId,
    required this.email,
    required this.fullname,
    required this.storeId,
    required this.status,
    required this.registered,
    required this.iso,
    required this.salesTaxName,
    required this.paymentEnable,
    required this.calcEnable,
    required this.trackEnable,
    required this.deliveryEnable,
    required this.prealertEnable,
    required this.pointsEnable,
    required this.referralEnable,
    required this.medSys,
    required this.eboxAddress,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      userId: json['user_id']?.toString() ?? '',
      email: json['email']?.toString() ?? '',
      fullname: json['fullname']?.toString() ?? '',
      storeId: json['store_id']?.toString() ?? '',
      status: json['status']?.toString() ?? '',
      registered: int.tryParse(json['registered']?.toString() ?? '0') ?? 0,
      iso: json['iso']?.toString() ?? '',
      salesTaxName: json['sales_tax_name']?.toString() ?? '',
      paymentEnable: json['payment_enable']?.toString() ?? '',
      calcEnable: json['calc_enable']?.toString() ?? '',
      trackEnable: json['track_enable']?.toString() ?? '',
      deliveryEnable: json['delivery_enable']?.toString() ?? '',
      prealertEnable: json['prealert_enable']?.toString() ?? '',
      pointsEnable: json['points_enable']?.toString() ?? '',
      referralEnable: json['referral_enable']?.toString() ?? '',
      medSys: json['med_sys']?.toString() ?? '',
      eboxAddress: EboxAddress.fromJson(json['ebox_address'] ?? {}),
    );
  }
}

class EboxAddress {
  String line1;
  String line2;
  String line3;
  String line4;

  EboxAddress({
    required this.line1,
    required this.line2,
    required this.line3,
    required this.line4,
  });

  factory EboxAddress.fromJson(Map<String, dynamic> json) {
    return EboxAddress(
      line1: json['line1']?.toString() ?? '',
      line2: json['line2']?.toString() ?? '',
      line3: json['line3']?.toString() ?? '',
      line4: json['line4']?.toString() ?? '',
    );
  }
}
