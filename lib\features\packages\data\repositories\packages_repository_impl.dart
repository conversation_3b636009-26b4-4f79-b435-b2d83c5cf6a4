import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/network/network_info.dart';
import '../../../../core/datasources/remote_data_source.dart';
import '../../domain/entities/package.dart';
import '../../domain/repositories/packages_repository.dart';
import '../dto/get_packages_dto.dart';

class PackagesRepositoryImpl implements PackagesRepository {
  final RemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  PackagesRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, PackagesResponse>> getPackages({
    required String userId,
    int page = 1,
    String? status,
    String? type,
    String? guia,
  }) async {
    if (await networkInfo.isConnected) {
      final petitionDto = GetPackagesDto(
        userId: userId,
        page: page,
        status: status,
        type: type,
        guia: guia,
      );
      
      try {
        final response = await remoteDataSource.petition<dynamic>(petitionDto);
        final packagesResponse = PackagesResponse.fromJson(response);
        return Right(packagesResponse);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure());
    }
  }
}
