import '../../../../core/dto/base_petition_dto.dart';

class GetPackagesDto extends BasePetitionDto {
  final String userId;
  final int page;
  final String? status;
  final String? type;
  final String? guia;

  const GetPackagesDto({
    required this.userId,
    this.page = 1,
    this.status,
    this.type,
    this.guia,
  }) : super(
    action: 'get_packages_v2',
    lang: 'es',
  );

  @override
  Map<String, dynamic> toJson() {
    final json = {
      ...super.toJson(),
      'country': 'VE',
      'user_id': userId,
      'page': page.toString(),
    };

    // Add optional filters only if they are not null or empty
    if (status != null && status!.isNotEmpty) {
      json['status'] = status!;
    }
    
    if (type != null && type!.isNotEmpty) {
      json['type'] = type!;
    }
    
    if (guia != null && guia!.isNotEmpty) {
      json['guia'] = guia!;
    }

    return json;
  }
}
